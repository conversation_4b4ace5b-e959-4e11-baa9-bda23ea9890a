const mongoose = require('mongoose');

const CircuitSchema = new mongoose.Schema({
  group: {
    type: String,
    required: [true, 'Please add a group'],
    trim: true
  },
  tournee: {
    type: String,
    required: [true, 'Please add a tournee'],
    trim: true
  },
  circuit: {
    type: String,
    required: [true, 'Please add a circuit'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound index for unique combination
CircuitSchema.index({ group: 1, tournee: 1, circuit: 1 }, { unique: true });

module.exports = mongoose.model('Circuit', CircuitSchema);
