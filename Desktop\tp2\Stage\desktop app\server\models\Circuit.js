const mongoose = require('mongoose');

const CircuitSchema = new mongoose.Schema({
  circuit_number: {
    type: Number,
    required: [true, 'Please add a circuit number']
  },
  direction: {
    type: String,
    required: [true, 'Please add a direction'],
    trim: true
  },
  agence: {
    type: String,
    required: [true, 'Please add an agence'],
    trim: true
  },
  groupe: {
    type: Number,
    required: [true, 'Please add a groupe']
  },
  tournee: {
    type: Number,
    required: [true, 'Please add a tournee']
  },
  number_of_subscribers: {
    type: Number,
    default: 0
  },
  active: {
    type: Boolean,
    default: true
  },
  created_at: {
    type: Date,
    default: Date.now
  }
});

// Create compound index for unique combination
CircuitSchema.index({ groupe: 1, tournee: 1, circuit_number: 1 }, { unique: true });

module.exports = mongoose.model('Circuit', CircuitSchema);
