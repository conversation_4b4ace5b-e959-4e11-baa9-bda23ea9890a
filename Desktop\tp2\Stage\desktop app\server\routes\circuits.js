const express = require('express');
const {
  createCircuit,
  getCircuits,
  getCircuit,
  updateCircuit,
  deleteCircuit,
  getGroups,
  getTourneesByGroup,
  getCircuitsByGroupAndTournee
} = require('../controllers/circuitController');
const { protect } = require('../middleware/auth');
const { authorize } = require('../middleware/roleCheck');

const router = express.Router();

router.use(protect);

// Get groups, tournees, and circuits for dropdowns
router.get('/groups', getGroups);
router.get('/tournees/:group', getTourneesByGroup);
router.get('/circuits/:group/:tournee', getCircuitsByGroupAndTournee);

router.route('/')
  .get(getCircuits)
  .post(authorize('admin'), createCircuit);

router.route('/:id')
  .get(getCircuit)
  .put(authorize('admin'), updateCircuit)
  .delete(authorize('admin'), deleteCircuit);

module.exports = router;
