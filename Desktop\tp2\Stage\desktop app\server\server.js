const express = require('express');
const cors = require('cors');
const connectDB = require('./config/db');
const config = require('./config/config');

// Connect to database
connectDB();

// Route files
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const readingSheetRoutes = require('./routes/readingSheets');
const readingRoutes = require('./routes/readings');
const circuitRoutes = require('./routes/circuits');

const app = express();

// Body parser
app.use(express.json());

// Enable CORS
app.use(cors());

// Mount routers
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/reading-sheets', readingSheetRoutes);
app.use('/api/readings', readingRoutes);
app.use('/api/circuits', circuitRoutes);

// Basic route for testing
app.get('/', (req, res) => {
  res.json({ message: 'API is running...' });
});

const PORT = config.PORT;

const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});
