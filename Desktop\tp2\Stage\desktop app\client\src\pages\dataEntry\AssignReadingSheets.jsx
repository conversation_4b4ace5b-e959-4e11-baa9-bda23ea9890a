import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import api from '../../services/api';
import ModernLayout from '../../components/Layout/ModernLayout';
import {
  FiFileText,
  FiUserCheck,
  FiArrowLeft,
  FiAlertCircle,
  FiUsers,
  FiSearch,
  FiMapPin,
  FiLayers
} from 'react-icons/fi';

const AssignReadingSheets = () => {
  const [readingSheets, setReadingSheets] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [tournees, setTournees] = useState([]);
  const [circuits, setCircuits] = useState([]);

  // Form state
  const [selectedGroup, setSelectedGroup] = useState('');
  const [selectedTournee, setSelectedTournee] = useState('');
  const [selectedCircuit, setSelectedCircuit] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');

  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch assigned reading sheets for progress display
        const sheetsRes = await api.get('/api/reading-sheets');
        const assignedSheets = sheetsRes.data.data.filter(
          sheet => sheet.status === 'assigned'
        );
        setReadingSheets(assignedSheets);

        // Fetch groups
        const groupsRes = await api.get('/api/circuits/groups');
        setGroups(groupsRes.data.data);

        try {
          // Fetch data entry users
          const usersRes = await api.get('/api/users');
          const dataEntryUsers = usersRes.data.data.filter(
            user => user.role === 'dataEntry' && user.active
          );
          setUsers(dataEntryUsers);
          setFilteredUsers(dataEntryUsers);
        } catch (userError) {
          console.error('Failed to fetch users:', userError);
          toast.error('Échec du chargement des utilisateurs. Vérifiez vos permissions.');
          setUsers([]);
          setFilteredUsers([]);
        }

        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch data:', error);
        toast.error('Échec du chargement des données');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle group selection
  const handleGroupChange = async (groupValue) => {
    setSelectedGroup(groupValue);
    setSelectedTournee('');
    setSelectedCircuit('');
    setTournees([]);
    setCircuits([]);

    if (groupValue) {
      try {
        const tourneesRes = await api.get(`/api/circuits/tournees/${groupValue}`);
        setTournees(tourneesRes.data.data);
      } catch (error) {
        console.error('Failed to fetch tournees:', error);
        toast.error('Échec du chargement des tournées');
      }
    }
  };

  // Handle tournee selection
  const handleTourneeChange = async (tourneeValue) => {
    setSelectedTournee(tourneeValue);
    setSelectedCircuit('');
    setCircuits([]);

    if (tourneeValue && selectedGroup) {
      try {
        const circuitsRes = await api.get(`/api/circuits/circuits/${selectedGroup}/${tourneeValue}`);
        setCircuits(circuitsRes.data.data);
      } catch (error) {
        console.error('Failed to fetch circuits:', error);
        toast.error('Échec du chargement des circuits');
      }
    }
  };

  // Handle user search
  const handleUserSearch = (searchTerm) => {
    setUserSearchTerm(searchTerm);
    if (searchTerm.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user =>
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.reference && user.reference.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    if (!selectedGroup || !selectedTournee || !selectedCircuit || !selectedUser) {
      toast.error('Veuillez sélectionner tous les champs requis');
      return;
    }

    setAssigning(true);

    try {
      // Find the selected circuit details
      const selectedCircuitData = circuits.find(c => c._id === selectedCircuit);

      // Generate a unique sheet number
      const sheetNumber = `G${selectedGroup}-T${selectedTournee}-C${selectedCircuitData?.circuit_number}-${Date.now()}`;

      // Create and assign reading sheet
      const readingSheetData = {
        sheetNumber,
        area: `Groupe ${selectedGroup} - Tournée ${selectedTournee}`,
        circuit: selectedCircuit,
        assignedTo: selectedUser,
        status: 'assigned',
        assignedDate: new Date()
      };

      await api.post('/api/reading-sheets', readingSheetData);

      toast.success('Fiche de relevé créée et assignée avec succès');

      // Reset form
      setSelectedGroup('');
      setSelectedTournee('');
      setSelectedCircuit('');
      setSelectedUser('');
      setUserSearchTerm('');
      setTournees([]);
      setCircuits([]);
      setFilteredUsers(users);

      // Refresh reading sheets
      const sheetsRes = await api.get('/api/reading-sheets');
      const assignedSheets = sheetsRes.data.data.filter(
        sheet => sheet.status === 'assigned'
      );
      setReadingSheets(assignedSheets);

    } catch (error) {
      toast.error('Échec de la création de la fiche de relevé');
      console.error('Error creating reading sheet:', error);
    } finally {
      setAssigning(false);
    }
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="flex items-center justify-center h-full min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="space-y-4">
        {/* Page Header - Formal Style */}
        <div className="bg-white p-5 border-b border-gray-200 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-xl font-medium text-gray-800">Assigner des Fiches de Relevé</h2>
              <p className="text-sm text-gray-500 mt-1">Attribution des fiches de relevé aux agents</p>
            </div>
            <div className="mt-3 md:mt-0">
              <Link
                to="/reading-sheets"
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 transition-colors duration-200"
              >
                <FiArrowLeft className="mr-1.5 h-4 w-4" />
                Retour aux fiches
              </Link>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Selection Area */}
          <div className="bg-white border border-gray-200 shadow-sm p-6">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-2 flex items-center">
                <FiLayers className="mr-2 h-5 w-5 text-primary-600" />
                Sélection de Zone et Attribution
              </h3>
              <p className="text-sm text-gray-500">
                Sélectionnez le groupe, la tournée, le circuit et l'attaché pour créer une nouvelle fiche de relevé.
              </p>
            </div>

            {users.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-yellow-50 rounded-full p-3 mb-4">
                  <FiAlertCircle className="h-8 w-8 text-yellow-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Aucun agent disponible</h3>
                <p className="text-gray-500 text-center max-w-md mb-4">
                  Il n'y a pas d'agents de saisie actifs disponibles pour l'assignation.
                </p>
                <Link
                  to="/reading-sheets"
                  className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                >
                  <FiFileText className="mr-2 h-4 w-4" />
                  Voir toutes les fiches
                </Link>
              </div>
            ) : (
              <form onSubmit={onSubmit} className="space-y-6">
                {/* Circuit Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="group" className="block text-sm font-medium text-gray-700 mb-1">
                      Groupe
                    </label>
                    <select
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                      id="group"
                      value={selectedGroup}
                      onChange={(e) => handleGroupChange(e.target.value)}
                      required
                    >
                      <option value="">-- Sélectionner un groupe --</option>
                      {groups.map(group => (
                        <option key={group} value={group}>
                          {group}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="tournee" className="block text-sm font-medium text-gray-700 mb-1">
                      Tournée
                    </label>
                    <select
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                      id="tournee"
                      value={selectedTournee}
                      onChange={(e) => handleTourneeChange(e.target.value)}
                      required
                      disabled={!selectedGroup}
                    >
                      <option value="">-- Sélectionner une tournée --</option>
                      {tournees.map(tournee => (
                        <option key={tournee} value={tournee}>
                          {tournee}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="circuit" className="block text-sm font-medium text-gray-700 mb-1">
                      Circuit
                    </label>
                    <select
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                      id="circuit"
                      value={selectedCircuit}
                      onChange={(e) => setSelectedCircuit(e.target.value)}
                      required
                      disabled={!selectedTournee}
                    >
                      <option value="">-- Sélectionner un circuit --</option>
                      {circuits.map(circuit => (
                        <option key={circuit._id} value={circuit._id}>
                          Circuit {circuit.circuit_number} - {circuit.direction}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* User Selection */}
                <div>
                  <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 mb-1">
                    L'Attaché (Recherche par nom ou référence)
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiSearch className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="userSearch"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                      placeholder="Rechercher par nom ou référence..."
                      value={userSearchTerm}
                      onChange={(e) => handleUserSearch(e.target.value)}
                    />
                  </div>

                  {/* User List */}
                  <div className="mt-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                    {filteredUsers.map(user => (
                      <div
                        key={user._id}
                        className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                          selectedUser === user._id ? 'bg-primary-50 border-primary-200' : ''
                        }`}
                        onClick={() => setSelectedUser(user._id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {user.firstName} {user.lastName}
                            </p>
                            {user.reference && (
                              <p className="text-xs text-gray-500">Réf: {user.reference}</p>
                            )}
                          </div>
                          {selectedUser === user._id && (
                            <div className="text-primary-600">
                              <FiUserCheck className="h-4 w-4" />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end">
                  <Link
                    to="/reading-sheets"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-gray-700 text-sm font-medium hover:bg-gray-50 mr-3"
                  >
                    Annuler
                  </Link>
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 transition-colors duration-200"
                    disabled={assigning}
                  >
                    {assigning ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Création en cours...
                      </>
                    ) : (
                      <>
                        <FiUserCheck className="mr-2 h-4 w-4" /> Assigner Fiche de Relevé
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>

          {/* Reading Progress */}
          <div className="bg-white border border-gray-200 shadow-sm p-6">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-800 mb-2 flex items-center">
                <FiMapPin className="mr-2 h-5 w-5 text-primary-600" />
                Progression des Relevés
              </h3>
              <p className="text-sm text-gray-500">
                Fiches de relevé actuellement assignées et en cours.
              </p>
            </div>

            {readingSheets.length === 0 ? (
              <div className="text-center py-8">
                <div className="bg-gray-50 rounded-full p-3 mx-auto w-16 h-16 flex items-center justify-center mb-4">
                  <FiAlertCircle className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500">Aucune fiche de relevé assignée pour le moment.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Numéro de Fiche
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Zone
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Circuit
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigné à
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date d'assignation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {readingSheets.map(sheet => (
                      <tr key={sheet._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {sheet.sheetNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.area}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.circuit ? `${sheet.circuit.group} - ${sheet.circuit.tournee} - ${sheet.circuit.circuit}` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.assignedTo ? `${sheet.assignedTo.firstName} ${sheet.assignedTo.lastName}` : 'Non assigné'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sheet.assignedDate ? new Date(sheet.assignedDate).toLocaleDateString('fr-FR') : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            sheet.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                            sheet.status === 'completed' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {sheet.status === 'assigned' ? 'Assigné' :
                             sheet.status === 'completed' ? 'Terminé' :
                             sheet.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </ModernLayout>
  );
};

export default AssignReadingSheets;
