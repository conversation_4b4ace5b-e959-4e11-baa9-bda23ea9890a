const mongoose = require('mongoose');
const Circuit = require('../models/Circuit');
require('dotenv').config({ path: '../.env' });

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const sampleCircuits = [
  // Group A
  { group: 'A', tournee: 'T1', circuit: 'C001', description: 'Circuit résidentiel centre-ville' },
  { group: 'A', tournee: 'T1', circuit: 'C002', description: 'Circuit commercial centre' },
  { group: 'A', tournee: 'T1', circuit: 'C003', description: 'Circuit industriel zone A' },
  { group: 'A', tournee: 'T2', circuit: 'C004', description: 'Circuit résidentiel nord' },
  { group: 'A', tournee: 'T2', circuit: 'C005', description: 'Circuit mixte nord-est' },
  { group: 'A', tournee: 'T3', circuit: 'C006', description: 'Circuit résidentiel sud' },
  { group: 'A', tournee: 'T3', circuit: 'C007', description: 'Circuit commercial sud' },

  // Group B
  { group: 'B', tournee: 'T1', circuit: 'C008', description: 'Circuit résidentiel ouest' },
  { group: 'B', tournee: 'T1', circuit: 'C009', description: 'Circuit commercial ouest' },
  { group: 'B', tournee: 'T2', circuit: 'C010', description: 'Circuit industriel zone B' },
  { group: 'B', tournee: 'T2', circuit: 'C011', description: 'Circuit mixte ouest' },
  { group: 'B', tournee: 'T3', circuit: 'C012', description: 'Circuit résidentiel périphérie' },

  // Group C
  { group: 'C', tournee: 'T1', circuit: 'C013', description: 'Circuit résidentiel est' },
  { group: 'C', tournee: 'T1', circuit: 'C014', description: 'Circuit commercial est' },
  { group: 'C', tournee: 'T2', circuit: 'C015', description: 'Circuit industriel zone C' },
  { group: 'C', tournee: 'T2', circuit: 'C016', description: 'Circuit mixte est' },
  { group: 'C', tournee: 'T3', circuit: 'C017', description: 'Circuit résidentiel banlieue' },
  { group: 'C', tournee: 'T3', circuit: 'C018', description: 'Circuit rural' }
];

const seedCircuits = async () => {
  try {
    await connectDB();
    
    // Clear existing circuits
    await Circuit.deleteMany({});
    console.log('Existing circuits cleared');
    
    // Insert sample circuits
    await Circuit.insertMany(sampleCircuits);
    console.log('Sample circuits inserted successfully');
    
    console.log(`${sampleCircuits.length} circuits have been added to the database`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding circuits:', error);
    process.exit(1);
  }
};

seedCircuits();
