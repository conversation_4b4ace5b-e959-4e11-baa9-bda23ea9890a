const Circuit = require('../models/Circuit');

// @desc    Create a new circuit
// @route   POST /api/circuits
// @access  Private/Admin
exports.createCircuit = async (req, res) => {
  try {
    const circuit = await Circuit.create(req.body);

    res.status(201).json({
      success: true,
      data: circuit
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Circuit combination already exists'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get all circuits
// @route   GET /api/circuits
// @access  Private
exports.getCircuits = async (req, res) => {
  try {
    const circuits = await Circuit.find({ active: true }).sort({ groupe: 1, tournee: 1, circuit_number: 1 });

    res.status(200).json({
      success: true,
      count: circuits.length,
      data: circuits
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get unique groups
// @route   GET /api/circuits/groups
// @access  Private
exports.getGroups = async (req, res) => {
  try {
    const groups = await Circuit.distinct('groupe', { active: true });

    res.status(200).json({
      success: true,
      data: groups.sort()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get tournees by group
// @route   GET /api/circuits/tournees/:group
// @access  Private
exports.getTourneesByGroup = async (req, res) => {
  try {
    const { group } = req.params;
    const tournees = await Circuit.distinct('tournee', { groupe: parseInt(group), active: true });

    res.status(200).json({
      success: true,
      data: tournees.sort()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get circuits by group and tournee
// @route   GET /api/circuits/circuits/:group/:tournee
// @access  Private
exports.getCircuitsByGroupAndTournee = async (req, res) => {
  try {
    const { group, tournee } = req.params;
    const circuits = await Circuit.find({
      groupe: parseInt(group),
      tournee: parseInt(tournee),
      active: true
    }).sort({ circuit_number: 1 });

    res.status(200).json({
      success: true,
      data: circuits
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get single circuit
// @route   GET /api/circuits/:id
// @access  Private
exports.getCircuit = async (req, res) => {
  try {
    const circuit = await Circuit.findById(req.params.id);

    if (!circuit) {
      return res.status(404).json({
        success: false,
        message: 'Circuit not found'
      });
    }

    res.status(200).json({
      success: true,
      data: circuit
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Update circuit
// @route   PUT /api/circuits/:id
// @access  Private/Admin
exports.updateCircuit = async (req, res) => {
  try {
    const circuit = await Circuit.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    if (!circuit) {
      return res.status(404).json({
        success: false,
        message: 'Circuit not found'
      });
    }

    res.status(200).json({
      success: true,
      data: circuit
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Circuit combination already exists'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Delete circuit
// @route   DELETE /api/circuits/:id
// @access  Private/Admin
exports.deleteCircuit = async (req, res) => {
  try {
    const circuit = await Circuit.findByIdAndUpdate(
      req.params.id,
      { active: false },
      { new: true }
    );

    if (!circuit) {
      return res.status(404).json({
        success: false,
        message: 'Circuit not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
