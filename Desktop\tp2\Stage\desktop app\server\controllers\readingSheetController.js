const ReadingSheet = require('../models/ReadingSheet');
const User = require('../models/User');
const Circuit = require('../models/Circuit');

// @desc    Create a new reading sheet
// @route   POST /api/reading-sheets
// @access  Private/DataEntry
exports.createReadingSheet = async (req, res) => {
  try {
    // Add user to req.body
    req.body.createdBy = req.user.id;

    const readingSheet = await ReadingSheet.create(req.body);

    res.status(201).json({
      success: true,
      data: readingSheet
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get all reading sheets
// @route   GET /api/reading-sheets
// @access  Private
exports.getReadingSheets = async (req, res) => {
  try {
    let query;

    // If user is not admin, show only assigned sheets
    if (req.user.role !== 'admin') {
      query = ReadingSheet.find({ assignedTo: req.user.id });
    } else {
      query = ReadingSheet.find();
    }

    // Add populate
    query = query.populate({
      path: 'assignedTo',
      select: 'firstName lastName reference'
    }).populate({
      path: 'createdBy',
      select: 'firstName lastName'
    }).populate({
      path: 'circuit',
      select: 'groupe tournee circuit_number direction agence'
    });

    const readingSheets = await query;

    res.status(200).json({
      success: true,
      count: readingSheets.length,
      data: readingSheets
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Get single reading sheet
// @route   GET /api/reading-sheets/:id
// @access  Private
exports.getReadingSheet = async (req, res) => {
  try {
    const readingSheet = await ReadingSheet.findById(req.params.id)
      .populate({
        path: 'assignedTo',
        select: 'name email'
      })
      .populate({
        path: 'createdBy',
        select: 'name email'
      });

    if (!readingSheet) {
      return res.status(404).json({
        success: false,
        message: 'Reading sheet not found'
      });
    }

    // Make sure user is the assigned user or an admin
    if (
      readingSheet.assignedTo &&
      readingSheet.assignedTo._id.toString() !== req.user.id &&
      req.user.role !== 'admin'
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this reading sheet'
      });
    }

    res.status(200).json({
      success: true,
      data: readingSheet
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Assign reading sheet to user
// @route   PUT /api/reading-sheets/:id/assign
// @access  Private/Admin
exports.assignReadingSheet = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a user ID'
      });
    }

    // Check if user exists
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get reading sheet
    let readingSheet = await ReadingSheet.findById(req.params.id);

    if (!readingSheet) {
      return res.status(404).json({
        success: false,
        message: 'Reading sheet not found'
      });
    }

    // Update reading sheet
    readingSheet.assignedTo = userId;
    readingSheet.status = 'assigned';
    readingSheet.assignedDate = Date.now();

    await readingSheet.save();

    res.status(200).json({
      success: true,
      data: readingSheet
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// @desc    Mark reading sheet as completed
// @route   PUT /api/reading-sheets/:id/complete
// @access  Private
exports.completeReadingSheet = async (req, res) => {
  try {
    let readingSheet = await ReadingSheet.findById(req.params.id);

    if (!readingSheet) {
      return res.status(404).json({
        success: false,
        message: 'Reading sheet not found'
      });
    }

    // Make sure user is the assigned user or an admin
    if (
      readingSheet.assignedTo &&
      readingSheet.assignedTo.toString() !== req.user.id &&
      req.user.role !== 'admin'
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this reading sheet'
      });
    }

    // Update reading sheet
    readingSheet.status = 'completed';
    readingSheet.completedDate = Date.now();

    await readingSheet.save();

    res.status(200).json({
      success: true,
      data: readingSheet
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
