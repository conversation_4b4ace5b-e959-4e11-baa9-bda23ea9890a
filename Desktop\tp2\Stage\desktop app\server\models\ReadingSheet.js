const mongoose = require('mongoose');

const ReadingSheetSchema = new mongoose.Schema({
  sheetNumber: {
    type: String,
    required: [true, 'Please add a sheet number'],
    unique: true,
    trim: true
  },
  assignedTo: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: false
  },
  status: {
    type: String,
    enum: ['unassigned', 'assigned', 'completed', 'archived'],
    default: 'unassigned'
  },
  area: {
    type: String,
    required: [true, 'Please add an area']
  },
  circuit: {
    type: mongoose.Schema.ObjectId,
    ref: 'Circuit',
    required: false
  },
  assignedDate: {
    type: Date
  },
  completedDate: {
    type: Date
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('ReadingSheet', ReadingSheetSchema);
